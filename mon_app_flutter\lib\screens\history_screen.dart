import 'package:flutter/material.dart';
import '../models/transaction.dart';
import '../services/database_service.dart';
import 'add_transaction_screen.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  final DatabaseService _databaseService = DatabaseService();
  String _filterType = 'all'; // all, revenue, expense
  String _sortBy = 'date'; // date, amount, title

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Historique des Transactions'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                _sortBy = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'date',
                child: Text('Trier par date'),
              ),
              const PopupMenuItem(
                value: 'amount',
                child: Text('Trier par montant'),
              ),
              const PopupMenuItem(
                value: 'title',
                child: Text('Trier par titre'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Filtres
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                const Text('Filtrer: '),
                const SizedBox(width: 8),
                Expanded(
                  child: SegmentedButton<String>(
                    segments: const [
                      ButtonSegment(
                        value: 'all',
                        label: Text('Tout'),
                        icon: Icon(Icons.list),
                      ),
                      ButtonSegment(
                        value: 'revenue',
                        label: Text('Revenus'),
                        icon: Icon(Icons.trending_up),
                      ),
                      ButtonSegment(
                        value: 'expense',
                        label: Text('Dépenses'),
                        icon: Icon(Icons.trending_down),
                      ),
                    ],
                    selected: {_filterType},
                    onSelectionChanged: (Set<String> selection) {
                      setState(() {
                        _filterType = selection.first;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),
          
          // Résumé
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildSummaryItem(
                      'Total Revenus',
                      _databaseService.getTotalRevenue(),
                      Colors.green,
                    ),
                    _buildSummaryItem(
                      'Total Dépenses',
                      _databaseService.getTotalExpenses(),
                      Colors.red,
                    ),
                    _buildSummaryItem(
                      'Profit',
                      _databaseService.getProfit(),
                      _databaseService.getProfit() >= 0 ? Colors.blue : Colors.orange,
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Liste des transactions
          Expanded(
            child: _buildTransactionsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddTransactionScreen(
                initialType: TransactionType.revenue,
              ),
            ),
          ).then((_) => setState(() {}));
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSummaryItem(String title, double amount, Color color) {
    return Column(
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${amount.toStringAsFixed(2)} €',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionsList() {
    List<Transaction> transactions = _getFilteredTransactions();
    _sortTransactions(transactions);

    if (transactions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Aucune transaction trouvée',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: transactions.length,
      itemBuilder: (context, index) {
        final transaction = transactions[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8.0),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: transaction.type == TransactionType.revenue
                  ? Colors.green
                  : Colors.red,
              child: Icon(
                transaction.type == TransactionType.revenue
                    ? Icons.add
                    : Icons.remove,
                color: Colors.white,
              ),
            ),
            title: Text(
              transaction.title,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (transaction.category != null)
                  Text('Catégorie: ${transaction.category}'),
                Text('Date: ${_formatDate(transaction.date)}'),
                if (transaction.description != null)
                  Text(
                    transaction.description!,
                    style: const TextStyle(fontStyle: FontStyle.italic),
                  ),
              ],
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${transaction.type == TransactionType.revenue ? '+' : '-'}${transaction.amount.toStringAsFixed(2)} €',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: transaction.type == TransactionType.revenue
                        ? Colors.green
                        : Colors.red,
                  ),
                ),
              ],
            ),
            onLongPress: () => _showDeleteDialog(transaction),
          ),
        );
      },
    );
  }

  List<Transaction> _getFilteredTransactions() {
    final allTransactions = _databaseService.getAllTransactions();
    
    switch (_filterType) {
      case 'revenue':
        return allTransactions
            .where((t) => t.type == TransactionType.revenue)
            .toList();
      case 'expense':
        return allTransactions
            .where((t) => t.type == TransactionType.expense)
            .toList();
      default:
        return allTransactions;
    }
  }

  void _sortTransactions(List<Transaction> transactions) {
    switch (_sortBy) {
      case 'amount':
        transactions.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case 'title':
        transactions.sort((a, b) => a.title.compareTo(b.title));
        break;
      case 'date':
      default:
        transactions.sort((a, b) => b.date.compareTo(a.date));
        break;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showDeleteDialog(Transaction transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer la transaction'),
        content: Text('Êtes-vous sûr de vouloir supprimer "${transaction.title}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              _databaseService.deleteTransaction(transaction.id);
              Navigator.pop(context);
              setState(() {});
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Transaction supprimée'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }
}
