import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/transaction.dart';

class SQLiteDatabaseService {
  static final SQLiteDatabaseService _instance = SQLiteDatabaseService._internal();
  factory SQLiteDatabaseService() => _instance;
  SQLiteDatabaseService._internal();

  static Database? _database;

  // Nom de la base de données
  static const String _databaseName = 'boutique_gestion.db';
  static const int _databaseVersion = 1;

  // Nom de la table
  static const String _tableName = 'transactions';

  // Colonnes de la table
  static const String _columnId = 'id';
  static const String _columnTitle = 'title';
  static const String _columnAmount = 'amount';
  static const String _columnType = 'type';
  static const String _columnDate = 'date';
  static const String _columnDescription = 'description';
  static const String _columnCategory = 'category';

  // Getter pour la base de données
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // Initialiser la base de données
  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  // Créer la table lors de la première création
  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $_tableName (
        $_columnId TEXT PRIMARY KEY,
        $_columnTitle TEXT NOT NULL,
        $_columnAmount REAL NOT NULL,
        $_columnType TEXT NOT NULL,
        $_columnDate TEXT NOT NULL,
        $_columnDescription TEXT,
        $_columnCategory TEXT
      )
    ''');

    // Ajouter des données d'exemple
    await _insertSampleData(db);
  }

  // Mise à jour de la base de données (pour les futures versions)
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Logique de mise à jour pour les futures versions
  }

  // Insérer des données d'exemple
  Future<void> _insertSampleData(Database db) async {
    final now = DateTime.now();
    
    final sampleTransactions = [
      Transaction(
        id: '1',
        title: 'Vente produit A',
        amount: 150.0,
        type: TransactionType.revenue,
        date: now.subtract(const Duration(days: 1)),
        description: 'Vente de produits alimentaires',
        category: 'Alimentaire',
      ),
      Transaction(
        id: '2',
        title: 'Vente produit B',
        amount: 200.0,
        type: TransactionType.revenue,
        date: now.subtract(const Duration(days: 2)),
        description: 'Vente de boissons',
        category: 'Boissons',
      ),
      Transaction(
        id: '3',
        title: 'Vente produit C',
        amount: 75.0,
        type: TransactionType.revenue,
        date: now.subtract(const Duration(days: 3)),
        description: 'Vente de snacks',
        category: 'Snacks',
      ),
      Transaction(
        id: '4',
        title: 'Achat stock',
        amount: 100.0,
        type: TransactionType.expense,
        date: now.subtract(const Duration(days: 1)),
        description: 'Réapprovisionnement stock',
        category: 'Stock',
      ),
      Transaction(
        id: '5',
        title: 'Électricité',
        amount: 50.0,
        type: TransactionType.expense,
        date: now.subtract(const Duration(days: 5)),
        description: 'Facture électricité',
        category: 'Utilities',
      ),
      Transaction(
        id: '6',
        title: 'Loyer',
        amount: 300.0,
        type: TransactionType.expense,
        date: now.subtract(const Duration(days: 7)),
        description: 'Loyer mensuel boutique',
        category: 'Loyer',
      ),
    ];

    for (final transaction in sampleTransactions) {
      await db.insert(_tableName, transaction.toMap());
    }
  }

  // Ajouter une transaction
  Future<int> addTransaction(Transaction transaction) async {
    final db = await database;
    return await db.insert(_tableName, transaction.toMap());
  }

  // Obtenir toutes les transactions
  Future<List<Transaction>> getAllTransactions() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      orderBy: '$_columnDate DESC',
    );

    return List.generate(maps.length, (i) {
      return Transaction.fromMap(maps[i]);
    });
  }

  // Supprimer une transaction
  Future<int> deleteTransaction(String id) async {
    final db = await database;
    return await db.delete(
      _tableName,
      where: '$_columnId = ?',
      whereArgs: [id],
    );
  }

  // Mettre à jour une transaction
  Future<int> updateTransaction(Transaction transaction) async {
    final db = await database;
    return await db.update(
      _tableName,
      transaction.toMap(),
      where: '$_columnId = ?',
      whereArgs: [transaction.id],
    );
  }

  // Obtenir les revenus
  Future<List<Transaction>> getRevenues() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: '$_columnType = ?',
      whereArgs: [TransactionType.revenue.toString()],
      orderBy: '$_columnDate DESC',
    );

    return List.generate(maps.length, (i) {
      return Transaction.fromMap(maps[i]);
    });
  }

  // Obtenir les dépenses
  Future<List<Transaction>> getExpenses() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: '$_columnType = ?',
      whereArgs: [TransactionType.expense.toString()],
      orderBy: '$_columnDate DESC',
    );

    return List.generate(maps.length, (i) {
      return Transaction.fromMap(maps[i]);
    });
  }

  // Calculer le total des revenus
  Future<double> getTotalRevenue() async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT SUM($_columnAmount) as total 
      FROM $_tableName 
      WHERE $_columnType = ?
    ''', [TransactionType.revenue.toString()]);

    return (result.first['total'] as double?) ?? 0.0;
  }

  // Calculer le total des dépenses
  Future<double> getTotalExpenses() async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT SUM($_columnAmount) as total 
      FROM $_tableName 
      WHERE $_columnType = ?
    ''', [TransactionType.expense.toString()]);

    return (result.first['total'] as double?) ?? 0.0;
  }

  // Calculer le profit
  Future<double> getProfit() async {
    final totalRevenue = await getTotalRevenue();
    final totalExpenses = await getTotalExpenses();
    return totalRevenue - totalExpenses;
  }

  // Obtenir les transactions par mois
  Future<List<Transaction>> getTransactionsByMonth(DateTime month) async {
    final db = await database;
    final startOfMonth = DateTime(month.year, month.month, 1);
    final endOfMonth = DateTime(month.year, month.month + 1, 0);

    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: '$_columnDate BETWEEN ? AND ?',
      whereArgs: [
        startOfMonth.toIso8601String(),
        endOfMonth.toIso8601String(),
      ],
      orderBy: '$_columnDate DESC',
    );

    return List.generate(maps.length, (i) {
      return Transaction.fromMap(maps[i]);
    });
  }

  // Obtenir les catégories uniques
  Future<List<String>> getCategories() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT DISTINCT $_columnCategory 
      FROM $_tableName 
      WHERE $_columnCategory IS NOT NULL 
      ORDER BY $_columnCategory
    ''');

    return maps
        .map((map) => map[_columnCategory] as String)
        .toList();
  }

  // Fermer la base de données
  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  // Supprimer la base de données (pour les tests)
  Future<void> deleteDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
