import 'package:flutter/material.dart';

class Animated<PERSON>hart extends StatefulWidget {
  final double revenueAmount;
  final double expenseAmount;
  final Duration animationDuration;

  const AnimatedChart({
    super.key,
    required this.revenueAmount,
    required this.expenseAmount,
    this.animationDuration = const Duration(milliseconds: 2000),
  });

  @override
  State<AnimatedChart> createState() => _AnimatedChartState();
}

class _AnimatedChartState extends State<AnimatedChart>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _revenueAnimation;
  late Animation<double> _expenseAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    final maxAmount = [widget.revenueAmount, widget.expenseAmount]
        .reduce((a, b) => a > b ? a : b);

    _revenueAnimation = Tween<double>(
      begin: 0,
      end: maxAmount > 0 ? widget.revenueAmount / maxAmount : 0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    _expenseAnimation = Tween<double>(
      begin: 0,
      end: maxAmount > 0 ? widget.expenseAmount / maxAmount : 0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    // Démarrer l'animation après un délai
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void didUpdateWidget(AnimatedChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.revenueAmount != widget.revenueAmount ||
        oldWidget.expenseAmount != widget.expenseAmount) {
      final maxAmount = [widget.revenueAmount, widget.expenseAmount]
          .reduce((a, b) => a > b ? a : b);

      _revenueAnimation = Tween<double>(
        begin: _revenueAnimation.value,
        end: maxAmount > 0 ? widget.revenueAmount / maxAmount : 0,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ));

      _expenseAnimation = Tween<double>(
        begin: _expenseAnimation.value,
        end: maxAmount > 0 ? widget.expenseAmount / maxAmount : 0,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ));

      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Comparaison Revenus vs Dépenses',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Column(
                  children: [
                    _buildBar(
                      'Revenus',
                      widget.revenueAmount,
                      _revenueAnimation.value,
                      Colors.green,
                      Icons.trending_up,
                    ),
                    const SizedBox(height: 16),
                    _buildBar(
                      'Dépenses',
                      widget.expenseAmount,
                      _expenseAnimation.value,
                      Colors.red,
                      Icons.trending_down,
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 20),
            _buildSummary(),
          ],
        ),
      ),
    );
  }

  Widget _buildBar(String label, double amount, double progress, Color color, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            Text(
              '${amount.toStringAsFixed(2)} €',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 12,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: Colors.grey[200],
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                gradient: LinearGradient(
                  colors: [
                    color,
                    color.withOpacity(0.7),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: color.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSummary() {
    final profit = widget.revenueAmount - widget.expenseAmount;
    final profitColor = profit >= 0 ? Colors.blue : Colors.orange;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          colors: [
            profitColor.withOpacity(0.1),
            profitColor.withOpacity(0.05),
          ],
        ),
      ),
      child: Row(
        children: [
          Icon(
            profit >= 0 ? Icons.account_balance_wallet : Icons.warning,
            color: profitColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Résultat',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '${profit.toStringAsFixed(2)} €',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: profitColor,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: profitColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              profit >= 0 ? 'Bénéfice' : 'Perte',
              style: TextStyle(
                color: profitColor,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
