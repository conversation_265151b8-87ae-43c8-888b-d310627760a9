# 🏪 Gestion Boutique - Application Flutter

Une application mobile de gestion financière pour boutiques développée avec Flutter.

## 📱 Fonctionnalités

### Dashboard Principal
- **Vue d'ensemble financière** : Affichage des revenus, dépenses et profits
- **Cartes de résumé** : Visualisation claire des métriques importantes
- **Actions rapides** : Boutons pour ajouter rapidement revenus et dépenses
- **Transactions récentes** : Liste des 5 dernières transactions

### Gestion des Transactions
- **Ajout de revenus** : Enregistrement des ventes et entrées d'argent
- **Ajout de dépenses** : Suivi des coûts et sorties d'argent
- **Catégorisation** : Organisation par catégories personnalisables
- **Descriptions** : Ajout de notes détaillées pour chaque transaction

### Historique et Analyse
- **Historique complet** : Liste de toutes les transactions
- **Filtrage** : Par type (revenus/dépenses) ou affichage global
- **Tri** : Par date, montant ou titre
- **Suppression** : Possibilité de supprimer des transactions (appui long)

### Calculs Automatiques
- **Profit en temps réel** : Calcul automatique (Revenus - Dépenses)
- **Totaux** : Sommes automatiques des revenus et dépenses
- **Indicateurs visuels** : Couleurs pour différencier gains et pertes

## 🚀 Installation et Lancement

### Prérequis
- Flutter SDK installé
- Un émulateur ou appareil connecté

### Commandes
```bash
# Cloner et naviguer dans le projet
cd mon_app_flutter

# Installer les dépendances
flutter pub get

# Lancer l'application
flutter run

# Pour le web
flutter run -d chrome --web-port 8080
```

## 🏗️ Architecture

### Structure des fichiers
```
lib/
├── main.dart                    # Point d'entrée
├── models/
│   └── transaction.dart         # Modèle de données
├── services/
│   └── database_service.dart    # Gestion des données
└── screens/
    ├── dashboard_screen.dart    # Écran principal
    ├── add_transaction_screen.dart # Ajout de transactions
    └── history_screen.dart      # Historique
```

### Modèle de données
- **Transaction** : Classe principale avec ID, titre, montant, type, date, description et catégorie
- **TransactionType** : Énumération pour différencier revenus et dépenses
- **DatabaseService** : Service singleton pour la gestion des données en mémoire

## 🎨 Interface Utilisateur

### Design
- **Material Design 3** : Interface moderne et cohérente
- **Thème vert** : Couleurs adaptées à la gestion financière
- **Cartes et élévations** : Hiérarchie visuelle claire
- **Icônes intuitives** : Navigation et actions facilement identifiables

### Navigation
- **AppBar** : Titre et actions contextuelles
- **FloatingActionButton** : Ajout rapide de transactions
- **Navigation par écrans** : Transitions fluides entre les vues

## 📊 Données de Démonstration

L'application inclut des données d'exemple pour tester les fonctionnalités :
- Ventes de produits alimentaires, boissons, snacks
- Dépenses : stock, électricité, loyer
- Différentes catégories et dates

## 🔧 Fonctionnalités Techniques

### Gestion d'État
- **StatefulWidget** : Mise à jour en temps réel de l'interface
- **setState()** : Rafraîchissement automatique des données

### Validation
- **Formulaires** : Validation des champs obligatoires
- **Types de données** : Vérification des montants numériques
- **Dates** : Sélecteur de date intégré

### Persistance
- **En mémoire** : Stockage temporaire pour cette version
- **Extensible** : Architecture prête pour base de données locale

## 🚀 Évolutions Possibles

- Persistance avec SQLite ou Hive
- Graphiques et statistiques avancées
- Export des données (PDF, Excel)
- Sauvegarde cloud
- Gestion multi-boutiques
- Notifications et rappels
- Mode sombre
- Authentification utilisateur
