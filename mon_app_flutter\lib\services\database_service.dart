import '../models/transaction.dart';
import 'sqlite_database_service.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  final SQLiteDatabaseService _sqliteService = SQLiteDatabaseService();

  // Obtenir toutes les transactions
  Future<List<Transaction>> getAllTransactions() async {
    return await _sqliteService.getAllTransactions();
  }

  // Ajouter une transaction
  Future<void> addTransaction(Transaction transaction) async {
    await _sqliteService.addTransaction(transaction);
  }

  // Supprimer une transaction
  Future<void> deleteTransaction(String id) async {
    await _sqliteService.deleteTransaction(id);
  }

  // Obtenir les revenus
  Future<List<Transaction>> getRevenues() async {
    return await _sqliteService.getRevenues();
  }

  // Obtenir les dépenses
  Future<List<Transaction>> getExpenses() async {
    return await _sqliteService.getExpenses();
  }

  // Calculer le total des revenus
  Future<double> getTotalRevenue() async {
    return await _sqliteService.getTotalRevenue();
  }

  // Calculer le total des dépenses
  Future<double> getTotalExpenses() async {
    return await _sqliteService.getTotalExpenses();
  }

  // Calculer le profit (revenus - dépenses)
  Future<double> getProfit() async {
    return await _sqliteService.getProfit();
  }

  // Obtenir les transactions par mois
  Future<List<Transaction>> getTransactionsByMonth(DateTime month) async {
    return await _sqliteService.getTransactionsByMonth(month);
  }

  // Obtenir les catégories uniques
  Future<List<String>> getCategories() async {
    return await _sqliteService.getCategories();
  }

  // Initialiser la base de données (appelé automatiquement)
  Future<void> initializeDatabase() async {
    // La base de données sera initialisée automatiquement avec des données d'exemple
    await _sqliteService.database;
  }
}
