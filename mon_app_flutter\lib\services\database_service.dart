import '../models/transaction.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  // Liste des transactions (en mémoire pour cette version simple)
  final List<Transaction> _transactions = [];

  // Obtenir toutes les transactions
  List<Transaction> getAllTransactions() {
    return List.from(_transactions);
  }

  // Ajouter une transaction
  void addTransaction(Transaction transaction) {
    _transactions.add(transaction);
  }

  // Supprimer une transaction
  void deleteTransaction(String id) {
    _transactions.removeWhere((transaction) => transaction.id == id);
  }

  // Obtenir les revenus
  List<Transaction> getRevenues() {
    return _transactions
        .where((transaction) => transaction.type == TransactionType.revenue)
        .toList();
  }

  // Obtenir les dépenses
  List<Transaction> getExpenses() {
    return _transactions
        .where((transaction) => transaction.type == TransactionType.expense)
        .toList();
  }

  // Calculer le total des revenus
  double getTotalRevenue() {
    return getRevenues()
        .fold(0.0, (sum, transaction) => sum + transaction.amount);
  }

  // Calculer le total des dépenses
  double getTotalExpenses() {
    return getExpenses()
        .fold(0.0, (sum, transaction) => sum + transaction.amount);
  }

  // Calculer le profit (revenus - dépenses)
  double getProfit() {
    return getTotalRevenue() - getTotalExpenses();
  }

  // Obtenir les transactions par mois
  List<Transaction> getTransactionsByMonth(DateTime month) {
    return _transactions.where((transaction) {
      return transaction.date.year == month.year &&
          transaction.date.month == month.month;
    }).toList();
  }

  // Obtenir les catégories uniques
  List<String> getCategories() {
    final categories = _transactions
        .where((transaction) => transaction.category != null)
        .map((transaction) => transaction.category!)
        .toSet()
        .toList();
    categories.sort();
    return categories;
  }

  // Ajouter des données de démonstration
  void addSampleData() {
    final now = DateTime.now();
    
    // Revenus d'exemple
    addTransaction(Transaction(
      id: '1',
      title: 'Vente produit A',
      amount: 150.0,
      type: TransactionType.revenue,
      date: now.subtract(const Duration(days: 1)),
      description: 'Vente de produits alimentaires',
      category: 'Alimentaire',
    ));

    addTransaction(Transaction(
      id: '2',
      title: 'Vente produit B',
      amount: 200.0,
      type: TransactionType.revenue,
      date: now.subtract(const Duration(days: 2)),
      description: 'Vente de boissons',
      category: 'Boissons',
    ));

    addTransaction(Transaction(
      id: '3',
      title: 'Vente produit C',
      amount: 75.0,
      type: TransactionType.revenue,
      date: now.subtract(const Duration(days: 3)),
      description: 'Vente de snacks',
      category: 'Snacks',
    ));

    // Dépenses d'exemple
    addTransaction(Transaction(
      id: '4',
      title: 'Achat stock',
      amount: 100.0,
      type: TransactionType.expense,
      date: now.subtract(const Duration(days: 1)),
      description: 'Réapprovisionnement stock',
      category: 'Stock',
    ));

    addTransaction(Transaction(
      id: '5',
      title: 'Électricité',
      amount: 50.0,
      type: TransactionType.expense,
      date: now.subtract(const Duration(days: 5)),
      description: 'Facture électricité',
      category: 'Utilities',
    ));

    addTransaction(Transaction(
      id: '6',
      title: 'Loyer',
      amount: 300.0,
      type: TransactionType.expense,
      date: now.subtract(const Duration(days: 7)),
      description: 'Loyer mensuel boutique',
      category: 'Loyer',
    ));
  }
}
