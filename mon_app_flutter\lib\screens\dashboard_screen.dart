import 'package:flutter/material.dart';
import '../models/transaction.dart';
import '../services/database_service.dart';
import '../widgets/animated_financial_card.dart';
import '../widgets/animated_transaction_tile.dart';
import '../widgets/animated_action_button.dart';
import '../widgets/animated_chart.dart';
import 'add_transaction_screen.dart';
import 'history_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final DatabaseService _databaseService = DatabaseService();
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() async {
    if (!_isInitialized) {
      await _databaseService.initializeDatabase();
      _isInitialized = true;
      setState(() {});
    }
  }

  void _refreshData() {
    setState(() {});
  }

  Future<void> _handleRefresh() async {
    // Simuler un délai de chargement pour l'animation
    await Future.delayed(const Duration(milliseconds: 1000));
    _refreshData();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, double>>(
      future: _getFinancialData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Gestion Boutique'),
              backgroundColor: Theme.of(context).colorScheme.inversePrimary,
            ),
            body: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasError) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Gestion Boutique'),
              backgroundColor: Theme.of(context).colorScheme.inversePrimary,
            ),
            body: Center(
              child: Text('Erreur: ${snapshot.error}'),
            ),
          );
        }

        final data = snapshot.data!;
        final totalRevenue = data['revenue']!;
        final totalExpenses = data['expenses']!;
        final profit = data['profit']!;

        return _buildDashboard(context, totalRevenue, totalExpenses, profit);
      },
    );
  }

  Future<Map<String, double>> _getFinancialData() async {
    final totalRevenue = await _databaseService.getTotalRevenue();
    final totalExpenses = await _databaseService.getTotalExpenses();
    final profit = await _databaseService.getProfit();

    return {
      'revenue': totalRevenue,
      'expenses': totalExpenses,
      'profit': profit,
    };
  }

  Widget _buildDashboard(BuildContext context, double totalRevenue, double totalExpenses, double profit) {

    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion Boutique'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const HistoryScreen(),
                ),
              ).then((_) => _refreshData());
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        color: Theme.of(context).primaryColor,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // Cartes de résumé financier
            Row(
              children: [
                Expanded(
                  child: AnimatedFinancialCard(
                    title: 'Revenus',
                    amount: totalRevenue,
                    color: Colors.green,
                    icon: Icons.trending_up,
                    animationDuration: const Duration(milliseconds: 1200),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: AnimatedFinancialCard(
                    title: 'Dépenses',
                    amount: totalExpenses,
                    color: Colors.red,
                    icon: Icons.trending_down,
                    animationDuration: const Duration(milliseconds: 1400),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            AnimatedFinancialCard(
              title: 'Profit',
              amount: profit,
              color: profit >= 0 ? Colors.blue : Colors.orange,
              icon: profit >= 0 ? Icons.account_balance_wallet : Icons.warning,
              animationDuration: const Duration(milliseconds: 1600),
            ),
            const SizedBox(height: 32),
            
            // Section des actions rapides
            const Text(
              'Actions rapides',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: AnimatedActionButton(
                    title: 'Ajouter Revenu',
                    icon: Icons.add_circle,
                    color: Colors.green,
                    onPressed: () => _navigateToAddTransaction(TransactionType.revenue),
                    animationDelay: const Duration(milliseconds: 800),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: AnimatedActionButton(
                    title: 'Ajouter Dépense',
                    icon: Icons.remove_circle,
                    color: Colors.red,
                    onPressed: () => _navigateToAddTransaction(TransactionType.expense),
                    animationDelay: const Duration(milliseconds: 1000),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),

            // Graphique de comparaison
            AnimatedChart(
              revenueAmount: totalRevenue,
              expenseAmount: totalExpenses,
            ),
            const SizedBox(height: 32),

            // Transactions récentes
            const Text(
              'Transactions récentes',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: FutureBuilder<List<Transaction>>(
                future: _databaseService.getAllTransactions(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (snapshot.hasError) {
                    return Center(child: Text('Erreur: ${snapshot.error}'));
                  }

                  final transactions = snapshot.data ?? [];
                  transactions.sort((a, b) => b.date.compareTo(a.date));
                  final recentTransactions = transactions.take(5).toList();

                  if (recentTransactions.isEmpty) {
                    return const Center(
                      child: Text('Aucune transaction enregistrée'),
                    );
                  }

                  return ListView.builder(
                    itemCount: recentTransactions.length,
                    itemBuilder: (context, index) {
                      final transaction = recentTransactions[index];
                      return AnimatedTransactionTile(
                        transaction: transaction,
                        index: index,
                        onTap: () {
                          // Optionnel : navigation vers les détails
                        },
                      );
                    },
                  );
                },
              ),
            ),
            ],
          ),
        ),
      ),
    );
  }







  void _navigateToAddTransaction(TransactionType type) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddTransactionScreen(initialType: type),
      ),
    ).then((_) => _refreshData());
  }
}
