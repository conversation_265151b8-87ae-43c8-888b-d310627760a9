import 'package:flutter/material.dart';
import '../models/transaction.dart';
import '../services/database_service.dart';
import '../widgets/financial_card.dart';
import '../widgets/transaction_tile.dart';
import 'add_transaction_screen.dart';
import 'history_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final DatabaseService _databaseService = DatabaseService();
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    if (!_isInitialized) {
      _databaseService.addSampleData();
      _isInitialized = true;
    }
  }

  void _refreshData() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final totalRevenue = _databaseService.getTotalRevenue();
    final totalExpenses = _databaseService.getTotalExpenses();
    final profit = _databaseService.getProfit();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion Boutique'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const HistoryScreen(),
                ),
              ).then((_) => _refreshData());
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cartes de résumé financier
            Row(
              children: [
                Expanded(
                  child: FinancialCard(
                    title: 'Revenus',
                    amount: totalRevenue,
                    color: Colors.green,
                    icon: Icons.trending_up,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: FinancialCard(
                    title: 'Dépenses',
                    amount: totalExpenses,
                    color: Colors.red,
                    icon: Icons.trending_down,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            FinancialCard(
              title: 'Profit',
              amount: profit,
              color: profit >= 0 ? Colors.blue : Colors.orange,
              icon: profit >= 0 ? Icons.account_balance_wallet : Icons.warning,
            ),
            const SizedBox(height: 32),
            
            // Section des actions rapides
            const Text(
              'Actions rapides',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    'Ajouter Revenu',
                    Icons.add_circle,
                    Colors.green,
                    () => _navigateToAddTransaction(TransactionType.revenue),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildActionButton(
                    'Ajouter Dépense',
                    Icons.remove_circle,
                    Colors.red,
                    () => _navigateToAddTransaction(TransactionType.expense),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),
            
            // Transactions récentes
            const Text(
              'Transactions récentes',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _buildRecentTransactions(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(String title, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: Colors.white),
      label: Text(
        title,
        style: const TextStyle(color: Colors.white),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
    );
  }

  Widget _buildRecentTransactions() {
    final transactions = _databaseService.getAllTransactions();
    transactions.sort((a, b) => b.date.compareTo(a.date));
    final recentTransactions = transactions.take(5).toList();

    if (recentTransactions.isEmpty) {
      return const Center(
        child: Text('Aucune transaction enregistrée'),
      );
    }

    return ListView.builder(
      itemCount: recentTransactions.length,
      itemBuilder: (context, index) {
        final transaction = recentTransactions[index];
        return TransactionTile(
          transaction: transaction,
          onTap: () {
            // Optionnel : navigation vers les détails
          },
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _navigateToAddTransaction(TransactionType type) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddTransactionScreen(initialType: type),
      ),
    ).then((_) => _refreshData());
  }
}
