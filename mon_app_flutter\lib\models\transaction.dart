enum TransactionType { revenue, expense }

class Transaction {
  final String id;
  final String title;
  final double amount;
  final TransactionType type;
  final DateTime date;
  final String? description;
  final String? category;

  Transaction({
    required this.id,
    required this.title,
    required this.amount,
    required this.type,
    required this.date,
    this.description,
    this.category,
  });

  // Convertir en Map pour la sauvegarde
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'amount': amount,
      'type': type.toString(),
      'date': date.toIso8601String(),
      'description': description,
      'category': category,
    };
  }

  // <PERSON><PERSON>er depuis Map
  factory Transaction.fromMap(Map<String, dynamic> map) {
    return Transaction(
      id: map['id'],
      title: map['title'],
      amount: map['amount'].toDouble(),
      type: TransactionType.values.firstWhere(
        (e) => e.toString() == map['type'],
      ),
      date: DateTime.parse(map['date']),
      description: map['description'],
      category: map['category'],
    );
  }

  // Copier avec modifications
  Transaction copyWith({
    String? id,
    String? title,
    double? amount,
    TransactionType? type,
    DateTime? date,
    String? description,
    String? category,
  }) {
    return Transaction(
      id: id ?? this.id,
      title: title ?? this.title,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      date: date ?? this.date,
      description: description ?? this.description,
      category: category ?? this.category,
    );
  }
}
